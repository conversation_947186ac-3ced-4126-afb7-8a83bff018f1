import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../domain/entities/income.dart';
import '../providers/income_providers.dart' as providers;

/// Handles pagination logic and UI for income list
class IncomePaginationHandler extends ConsumerWidget {
  final ScrollController scrollController;
  final String entityName;
  final IconData entityIcon;
  final Widget Function(Income) buildListItem;

  const IncomePaginationHandler({
    super.key,
    required this.scrollController,
    required this.entityName,
    required this.entityIcon,
    required this.buildListItem,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Optimize: Only rebuild when the actual data changes, not on loading state changes
    final paginatedIncomeAsync = ref.watch(
      providers.paginatedIncomeListProvider.select((asyncValue) {
        return asyncValue.whenOrNull(data: (data) => data);
      }),
    );

    // Handle the optimized data
    if (paginatedIncomeAsync != null) {
      return _buildPaginatedIncomeList(context, paginatedIncomeAsync);
    }

    // Handle error and loading states when data is null
    final fullAsyncValue = ref.watch(providers.paginatedIncomeListProvider);

    return fullAsyncValue.maybeWhen(
      error: (error, stack) => SliverFillRemaining(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error, size: 64, color: Colors.red[400]),
              const SizedBox(height: 16),
              Text(
                'Error loading data',
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(color: Colors.red[600]),
              ),
              const SizedBox(height: 8),
              Text(
                error.toString(),
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
      orElse: () => const SliverToBoxAdapter(child: SizedBox.shrink()),
    );
  }

  /// Build paginated list with loading indicator
  Widget _buildPaginatedIncomeList(
    BuildContext context,
    providers.PaginatedIncomeResult paginatedResult,
  ) {
    final incomeList = paginatedResult.incomeList;
    final hasMorePages = paginatedResult.hasMorePages;

    if (incomeList.isEmpty) {
      return SliverFillRemaining(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(entityIcon, size: 64, color: Colors.grey[400]),
              const SizedBox(height: 16),
              Text(
                'No ${entityName.toLowerCase()}s found',
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(color: Colors.grey[600]),
              ),
              const SizedBox(height: 8),
              Text(
                'Tap the + button to add your first ${entityName.toLowerCase()}',
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
              ),
            ],
          ),
        ),
      );
    }

    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          // If we're at the last item and there are more pages, show a loading indicator
          if (index == incomeList.length) {
            return hasMorePages
                ? const Padding(
                    padding: EdgeInsets.symmetric(vertical: 16.0),
                    child: Center(child: CircularProgressIndicator()),
                  )
                : const SizedBox(
                    height: 40,
                  ); // Extra space at the end of the list
          }

          final income = incomeList[index];
          return buildListItem(income);
        },
        // Add +1 for the loading indicator or extra space
        childCount: incomeList.length + 1,
      ),
    );
  }
}

/// Mixin to handle pagination scroll logic
mixin IncomePaginationMixin<T extends ConsumerStatefulWidget>
    on ConsumerState<T> {
  late ScrollController scrollController;

  @override
  void initState() {
    super.initState();
    scrollController = ScrollController();
    scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    scrollController.removeListener(_onScroll);
    scrollController.dispose();
    super.dispose();
  }

  /// Called when the user scrolls - handles pagination
  void _onScroll() {
    // Check if we're at the bottom of the list
    if (scrollController.position.pixels >=
        scrollController.position.maxScrollExtent - 200) {
      // Load more data when we're near the end
      final paginatedIncomeNotifier = ref.read(
        providers.paginatedIncomeListProvider.notifier,
      );
      paginatedIncomeNotifier.loadNextPage();
    }
  }
}
