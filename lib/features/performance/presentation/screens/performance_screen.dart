import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';

import '../../../../core/presentation/screens/base_crud_screen.dart';
import '../../../../core/utils/date_helper.dart';
import '../../../../core/utils/snackbar_utils.dart';
import '../../../../core/widgets/app_delete_confirmation_dialog.dart';
import '../../../../core/widgets/item_actions_bottom_sheet.dart';
import '../../domain/entities/performance.dart';
import '../constants/performance_constants.dart';
import '../providers/performance_providers.dart';
import '../screens/performance_form_screen.dart';
import '../widgets/performance_details_sheet.dart';
import '../widgets/performance_list_item.dart';
import '../widgets/performance_unified_shimmer_loading.dart';

class PerformanceScreen extends BaseCrudScreen<Performance, dynamic> {
  const PerformanceScreen({super.key});

  @override
  PerformanceScreenState createState() => PerformanceScreenState();
}

class PerformanceScreenState
    extends BaseCrudScreenState<Performance, dynamic, PerformanceScreen> {
  // Abstract properties implementation
  @override
  String get screenTitle => PerformanceConstants.screenTitle;

  @override
  String get addButtonTooltip => 'Add new performance record';

  @override
  IconData get entityIcon => Icons.insights;

  @override
  String get entityName => 'Performance Record';

  // Abstract methods implementation
  @override
  AsyncValue<List<Performance>> get entityListAsync =>
      ref.watch(performanceListProvider);

  @override
  AsyncValue<dynamic> get summaryAsync => const AsyncValue.data(null); // No summary for Performance

  @override
  Widget buildListItem(Performance entity) {
    return PerformanceListItem(
      performance: entity,
      onTap: (performance) => _showPerformanceDetails(context, performance),
      onLongPress: (performance, orderCount) =>
          _showActionsBottomSheet(context, performance, orderCount),
    );
  }

  @override
  void navigateToForm({Performance? entity}) {
    Navigator.of(context)
        .push(
          MaterialPageRoute(
            builder: (context) => PerformanceFormScreen(performance: entity),
          ),
        )
        .then((_) {
          // Refresh the list when returning from the form screen
          ref.invalidate(performanceListProvider);
        });
  }

  @override
  Future<void> deleteEntity(Performance entity) async {
    if (entity.id == null) return;

    // Show loading indicator
    SnackbarUtils.showLoading(message: 'Deleting...');

    // Use the provider to delete the performance record
    final success = await ref
        .read(performanceOperationsProvider.notifier)
        .deletePerformance(entity.id!);

    if (mounted) {
      if (success) {
        SnackbarUtils.showSuccess(PerformanceConstants.deleteSuccessMessage);

        // Refresh the list
        ref.invalidate(performanceListProvider);
      } else {
        SnackbarUtils.showError(PerformanceConstants.deleteErrorPrefix);
      }
    }
  }

  @override
  Widget buildSummarySection(dynamic summary) {
    // Performance screen doesn't have a summary section, just return empty
    return const SizedBox.shrink();
  }

  @override
  Widget buildHistoryHeader() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.fromLTRB(16.0, 16.0, 16.0, 8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              PerformanceConstants.historyTitle,
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            Padding(
              padding: const EdgeInsets.only(bottom: 8.0),
              child: Text(
                PerformanceConstants.historySubtitle,
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget buildLoadingState() {
    return const PerformanceUnifiedShimmerLoading();
  }

  @override
  void refreshData() {
    ref.invalidate(performanceListProvider);
  }

  // Helper methods for Performance-specific functionality

  void _showActionsBottomSheet(
    BuildContext context,
    Performance performance,
    int orderCount,
  ) {
    ItemActionsBottomSheet.show(
      context: context,
      title: PerformanceConstants.screenTitle,
      subtitle: 'Date: ${DateHelper.formatForDisplay(performance.date)}',
      onEdit: () => navigateToForm(entity: performance),
      onDelete: () => _showDeleteConfirmation(context, performance),
      itemIcon: Icons.insights,
    );
  }

  void _showDeleteConfirmation(
    BuildContext context,
    Performance performance,
  ) async {
    final confirmed = await AppDeleteConfirmationDialog.showForRecord(
      context: context,
      recordType: PerformanceConstants.screenTitle,
      recordIdentifier: DateFormat('dd MMM yyyy').format(performance.date),
    );

    if (confirmed == true) {
      await deleteEntity(performance);
    }
  }

  void _showPerformanceDetails(BuildContext context, Performance performance) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => PerformanceDetailsSheet(performance: performance),
    );
  }
}
