import 'package:dartz/dartz.dart';
import 'package:flutter/material.dart';
import 'package:riverpod/riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../core/errors/failures.dart';
import '../../../../core/providers/global_date_range_provider.dart';
import '../../../../core/providers/mixins/crud_provider_template.dart';
import '../../../../core/providers/mixins/provider_error_handling.dart'
    as mixins;
import '../../../../core/providers/provider_templates.dart';
import '../../../../core/services/calculation/providers/calculation_providers.dart';
import '../../../../core/utils/date_helper.dart';
import '../../../orders/presentation/providers/order_providers.dart';
import '../../data/repositories/performance_repository_impl.dart';
import '../../domain/entities/performance.dart';
import '../../domain/repositories/performance_repository.dart';
import '../../domain/use_cases/calculate_performance_metrics.dart';
import '../../domain/use_cases/calculate_retention.dart';
import '../../domain/use_cases/get_14day_metrics.dart';

part 'performance_providers.g.dart';

// Provider for 14-day metrics use case
@riverpod
Get14DayMetrics get14DayMetrics(Ref ref) {
  return createUseCaseProviderMultiDeps<Get14DayMetrics>(
    ref,
    () => Get14DayMetrics(
      ref.watch(orderRepositoryProvider),
      ref.watch(performanceRepositoryProvider),
    ),
  );
}

// Provider for calculate retention use case
@riverpod
CalculateRetention calculateRetention(Ref ref) {
  return createUseCaseProviderMultiDeps<CalculateRetention>(
    ref,
    () => CalculateRetention(ref.watch(calculationServiceProvider)),
  );
}

// Provider for calculate performance metrics use case
@riverpod
CalculatePerformanceMetrics calculatePerformanceMetrics(Ref ref) {
  return createUseCaseProviderMultiDeps<CalculatePerformanceMetrics>(
    ref,
    () => CalculatePerformanceMetrics(
      performanceRepository: ref.watch(performanceRepositoryProvider),
      orderRepository: ref.watch(orderRepositoryProvider),
    ),
  );
}

// Provider for performance repository
@riverpod
PerformanceRepository performanceRepository(Ref ref) {
  return createRepositoryProviderWithDeps<
    PerformanceRepository,
    PerformanceRepositoryImpl
  >(
    ref,
    (database, syncService) => PerformanceRepositoryImpl(
      database: database,
      calculateRetention: ref.watch(calculateRetentionProvider),
      orderRepository: ref.watch(orderRepositoryProvider),
      syncService: syncService,
    ),
  );
}

// Provider for performance list
@riverpod
Future<List<Performance>> performanceList(Ref ref) async {
  final repository = ref.watch(performanceRepositoryProvider);
  final dateRangeAsync = ref.watch(globalDateRangeProvider);
  final result = await repository.getAllPerformance();

  return result.fold(
    (failure) {
      debugPrint('Error fetching performance: ${failure.toString()}');
      return [];
    },
    (performanceList) {
      // No filtering needed if list is empty
      if (performanceList.isEmpty) {
        return [];
      }

      // Wait for date range to be available
      if (!dateRangeAsync.hasValue) {
        return performanceList;
      }

      final dateRange = dateRangeAsync.value!;

      // Filter by date range
      // Ensure date range is in UTC for consistent comparison
      final startUtc = DateHelper.ensureUtc(dateRange.start);
      final endUtc = DateHelper.ensureUtc(
        dateRange.end.add(const Duration(days: 1)),
      );

      return performanceList.where((performance) {
        // Ensure performance date is in UTC for consistent comparison
        final dateUtc = DateHelper.ensureUtc(performance.date);

        return dateUtc.isAtSameMomentAs(startUtc) ||
            dateUtc.isAtSameMomentAs(endUtc) ||
            (dateUtc.isAfter(startUtc) && dateUtc.isBefore(endUtc));
      }).toList();
    },
  );
}

// Provider for performance operations
@riverpod
class PerformanceOperations extends _$PerformanceOperations
    with mixins.ProviderErrorHandling, CrudProviderTemplate<Performance> {
  @override
  Future<void> build() async {}

  @override
  List<ProviderBase> get invalidateProviders => [performanceListProvider];

  Future<bool> addPerformance(Performance performance) async {
    state = const AsyncValue.loading();
    final repository = ref.read(performanceRepositoryProvider);
    final result = await repository.savePerformance(performance);

    return result.fold(
      (failure) {
        // Use the mixin's error handling
        handleResult<Performance>(Left(failure), performance);
        state = AsyncValue.error(failure.toString(), StackTrace.current);
        return false;
      },
      (_) {
        ref.invalidate(performanceListProvider);
        state = const AsyncValue.data(null);
        return true;
      },
    );
  }

  Future<bool> updatePerformance(Performance performance) async {
    state = const AsyncValue.loading();
    final repository = ref.read(performanceRepositoryProvider);
    final result = await repository.updatePerformance(performance);

    return result.fold(
      (failure) {
        // Use the mixin's error handling
        handleResult<Performance>(Left(failure), performance);
        state = AsyncValue.error(failure.toString(), StackTrace.current);
        return false;
      },
      (_) {
        ref.invalidate(performanceListProvider);
        state = const AsyncValue.data(null);
        return true;
      },
    );
  }

  Future<bool> deletePerformance(int id) async {
    state = const AsyncValue.loading();
    final repository = ref.read(performanceRepositoryProvider);
    final result = await repository.deletePerformance(id);

    return result.fold(
      (failure) {
        // Use the mixin's error handling
        handleResult<bool>(Left(failure), false);
        state = AsyncValue.error(failure.toString(), StackTrace.current);
        return false;
      },
      (success) {
        ref.invalidate(performanceListProvider);
        state = const AsyncValue.data(null);
        return success;
      },
    );
  }
}

// Provider for performance metrics for the last 14 days
@riverpod
class PerformanceMetricsData extends _$PerformanceMetricsData {
  @override
  Future<PerformanceMetrics> build() async {
    // We use today's date as the endDate, but the calculation will exclude today
    // and use the previous 14 days (e.g., if today is March 25, it will use March 11-24)
    final endDate = DateTime.now();
    return _fetchPerformanceMetrics(endDate);
  }

  Future<PerformanceMetrics> _fetchPerformanceMetrics(DateTime endDate) async {
    final get14DayMetrics = ref.watch(get14DayMetricsProvider);
    final result = await get14DayMetrics.execute(endDate);

    return result.fold((failure) {
      _handleFailure(failure);
      return PerformanceMetrics.empty();
    }, (metrics) => metrics);
  }

  void _handleFailure(Failure failure) {
    debugPrint('Error fetching performance metrics: ${failure.toString()}');
  }

  Future<void> refreshMetrics() async {
    state = const AsyncValue.loading();
    final endDate = DateTime.now().subtract(const Duration(days: 1));
    state = await AsyncValue.guard(() => _fetchPerformanceMetrics(endDate));
  }

  Future<void> setEndDate(DateTime endDate) async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() => _fetchPerformanceMetrics(endDate));
  }
}

// Provider for online hours input
@riverpod
class OnlineHours extends _$OnlineHours {
  @override
  double build() {
    return 8.0; // Default value
  }

  void setOnlineHours(double hours) {
    state = hours;
  }
}

// Provider for retention calculation
@riverpod
Future<double> retention(Ref ref) async {
  final dateRangeAsync = ref.watch(globalDateRangeProvider);
  final onlineHours = ref.watch(onlineHoursProvider);

  // If date range is not available, use default calculation
  if (!dateRangeAsync.hasValue) {
    final metrics = await ref.watch(performanceMetricsDataProvider.future);
    if (metrics.avgCompletedPerDay == 0) return 0;
    return (onlineHours * 60) / metrics.avgCompletedPerDay;
  }

  // Use the new use case for calculation
  final dateRange = dateRangeAsync.value!;
  final calculateMetrics = ref.watch(calculatePerformanceMetricsProvider);

  final result = await calculateMetrics.execute(dateRange.start, dateRange.end);

  return result.fold((failure) => 0.0, (metrics) {
    final avgCompletedOrders = metrics['avgCompletedOrders'] as double;
    if (avgCompletedOrders == 0) return 0.0;
    return (onlineHours * 60) / avgCompletedOrders;
  });
}
