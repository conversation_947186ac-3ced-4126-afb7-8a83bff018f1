/// Template for creating list providers with standardized patterns
///
/// This mixin provides a standardized template for creating list providers
/// that handle loading states, error handling, and refresh functionality
/// in a consistent manner across all features.
///
/// Usage:
/// ```dart
/// @riverpod
/// class FeatureList extends _$FeatureList
///     with ProviderErrorHandling, ListProviderTemplate<Feature> {
///   @override
///   Future<List<Feature>> build() => fetchList();
///
///   @override
///   Future<List<Feature>> fetchData() async {
///     final useCase = ref.watch(getFeatureListProvider);
///     final result = await useCase.getAll();
///     return handleResult(result, []);
///   }
///
///   @override
///   List<Provider> get invalidateProviders => [featureListProvider];
/// }
/// ```
library;

import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'provider_error_handling.dart';

/// Template for creating list providers with refresh capability
///
/// This mixin standardizes the pattern of creating list providers that
/// can be refreshed and handle loading states. It must be used together
/// with the ProviderErrorHandling mixin to provide consistent error handling.
///
/// The template provides:
/// - Standardized list fetching with error handling
/// - Refresh functionality with provider invalidation
/// - Loading state management
/// - Generic type support for different list item types
///
/// Requirements:
/// - Must be mixed with ProviderErrorHandling
/// - Must implement fetchData() method
/// - Must implement invalidateProviders getter
mixin ListProviderTemplate<T> on ProviderErrorHandling {
  /// Fetch the list data - must be implemented by concrete classes
  ///
  /// This method should contain the actual data fetching logic specific
  /// to each feature. It should return a Future&lt;List&lt;T&gt;&gt; and use the
  /// handleResult() method from ProviderErrorHandling for error handling.
  ///
  /// Example implementation:
  /// ```dart
  /// @override
  /// Future<List<Order>> fetchData() async {
  ///   final useCase = ref.watch(getOrderListProvider);
  ///   final result = await useCase.getAll();
  ///   return handleResult(result, []);
  /// }
  /// ```
  Future<List<T>> fetchData();

  /// Providers to invalidate when refreshing - must be implemented by concrete classes
  ///
  /// This getter should return a list of providers that need to be invalidated
  /// when the list is refreshed. This ensures that dependent providers are
  /// also updated when the data changes.
  ///
  /// Example implementation:
  /// ```dart
  /// @override
  /// List<Provider> get invalidateProviders => [
  ///   orderListProvider,
  ///   orderSummaryProvider,
  /// ];
  /// ```
  List<ProviderBase> get invalidateProviders;

  /// Standard list fetching with error handling
  ///
  /// This method provides the standardized implementation for fetching lists.
  /// It delegates to the fetchData() method which must be implemented by
  /// concrete classes. This allows for consistent behavior while maintaining
  /// flexibility for feature-specific logic.
  ///
  /// This method should be called from the build() method of AsyncNotifier
  /// classes to provide the standardized list fetching behavior.
  ///
  /// Returns:
  /// - A Future&lt;List&lt;T&gt;&gt; containing the fetched data
  /// - An empty list if an error occurs (handled by ProviderErrorHandling)
  Future<List<T>> fetchList() async {
    return await fetchData();
  }

  /// Refresh the list and invalidate related providers
  ///
  /// This method provides a standardized way to refresh list data and
  /// invalidate related providers. It should be called when the list
  /// needs to be refreshed due to user action or data changes.
  ///
  /// The method:
  /// 1. Invalidates all providers specified in invalidateProviders
  /// 2. Triggers a rebuild of the current provider
  ///
  /// Note: This method requires access to WidgetRef which should be
  /// provided by the concrete class implementation.
  ///
  /// Example usage in a provider:
  /// ```dart
  /// Future<void> refreshOrderList() async {
  ///   await refreshList(ref);
  /// }
  /// ```
  Future<void> refreshList(WidgetRef ref) async {
    // Invalidate related providers to ensure consistency
    for (final provider in invalidateProviders) {
      ref.invalidate(provider);
    }
  }

  /// Get loading state indicator
  ///
  /// This method can be used to check if the list is currently being loaded.
  /// It's useful for showing loading indicators in the UI.
  ///
  /// Note: The actual loading state management is handled by Riverpod's
  /// AsyncValue system. This method is provided for convenience and
  /// future extensibility.
  bool get isLoading => false; // Default implementation

  /// Get error state indicator
  ///
  /// This method can be used to check if the list has encountered an error.
  /// It's useful for showing error states in the UI.
  ///
  /// Note: The actual error state management is handled by Riverpod's
  /// AsyncValue system. This method is provided for convenience and
  /// future extensibility.
  bool get hasError => false; // Default implementation

  /// Get empty state indicator
  ///
  /// This method can be used to check if the list is empty.
  /// It's useful for showing empty state messages in the UI.
  ///
  /// Parameters:
  /// - [data]: The current list data to check
  ///
  /// Returns:
  /// - true if the list is empty, false otherwise
  bool isEmpty(List<T> data) => data.isEmpty;

  /// Get item count
  ///
  /// This method provides a convenient way to get the number of items
  /// in the list. It's useful for displaying counts in the UI.
  ///
  /// Parameters:
  /// - [data]: The current list data to count
  ///
  /// Returns:
  /// - The number of items in the list
  int getItemCount(List<T> data) => data.length;
}
