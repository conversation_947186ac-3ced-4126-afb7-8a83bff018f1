import 'package:hooks_riverpod/hooks_riverpod.dart';

/// Optimized invalidation strategies to prevent overly aggressive data refetching
/// 
/// This file provides utilities and patterns for smart provider invalidation
/// that minimizes unnecessary network requests and improves performance.

/// Mixin for smart invalidation strategies
mixin OptimizedInvalidationMixin {
  /// Invalidate providers with debouncing to prevent rapid successive invalidations
  static final Map<String, DateTime> _lastInvalidationTimes = {};
  static const Duration _debounceThreshold = Duration(milliseconds: 500);

  /// Debounced invalidation - only invalidates if enough time has passed
  void invalidateWithDebounce(
    WidgetRef ref,
    ProviderBase provider, {
    Duration? customThreshold,
  }) {
    final threshold = customThreshold ?? _debounceThreshold;
    final providerKey = provider.toString();
    final now = DateTime.now();
    final lastInvalidation = _lastInvalidationTimes[providerKey];

    if (lastInvalidation == null || 
        now.difference(lastInvalidation) > threshold) {
      ref.invalidate(provider);
      _lastInvalidationTimes[providerKey] = now;
    }
  }

  /// Batch invalidation - invalidate multiple providers in a single operation
  void batchInvalidate(WidgetRef ref, List<ProviderBase> providers) {
    for (final provider in providers) {
      ref.invalidate(provider);
    }
  }

  /// Conditional invalidation - only invalidate if condition is met
  void invalidateIf(
    WidgetRef ref,
    ProviderBase provider,
    bool condition,
  ) {
    if (condition) {
      ref.invalidate(provider);
    }
  }

  /// Smart refresh - only refresh if data is stale
  void refreshIfStale(
    WidgetRef ref,
    ProviderBase provider, {
    Duration staleDuration = const Duration(minutes: 5),
  }) {
    // This would require additional state tracking in providers
    // For now, we provide the pattern for implementation
    ref.invalidate(provider);
  }
}

/// Provider invalidation groups for related data
class InvalidationGroups {
  /// Income-related providers that should be invalidated together
  static const List<String> incomeGroup = [
    'incomeListProvider',
    'paginatedIncomeListProvider',
    'incomeSummaryProvider',
  ];

  /// Order-related providers that should be invalidated together
  static const List<String> orderGroup = [
    'orderListProvider',
    'orderSummaryProvider',
    'performanceMetricsProvider',
  ];

  /// Performance-related providers that should be invalidated together
  static const List<String> performanceGroup = [
    'performanceListProvider',
    'performanceMetricsProvider',
    'levelCalculationProvider',
  ];

  /// Date-sensitive providers that should be invalidated when date range changes
  static const List<String> dateRangeGroup = [
    'incomeListProvider',
    'orderListProvider',
    'performanceListProvider',
    'incomeSummaryProvider',
    'orderSummaryProvider',
  ];
}

/// Smart invalidation strategies for different scenarios
class InvalidationStrategies {
  /// Strategy for CRUD operations - only invalidate necessary providers
  static void onCrudOperation(
    WidgetRef ref,
    String entityType,
    String operation,
  ) {
    switch (entityType.toLowerCase()) {
      case 'income':
        _invalidateIncomeProviders(ref, operation);
        break;
      case 'order':
        _invalidateOrderProviders(ref, operation);
        break;
      case 'performance':
        _invalidatePerformanceProviders(ref, operation);
        break;
    }
  }

  /// Strategy for date range changes - invalidate only date-sensitive providers
  static void onDateRangeChange(WidgetRef ref) {
    // Only invalidate providers that depend on date range
    // Don't invalidate settings, user preferences, etc.
    final dateRangeProviders = [
      // Add actual provider references here
    ];
    
    for (final provider in dateRangeProviders) {
      ref.invalidate(provider);
    }
  }

  /// Strategy for backup restore - selective invalidation
  static void onBackupRestore(WidgetRef ref) {
    // Invalidate data providers but not UI state providers
    // This prevents losing user's current UI state (like selected tabs, etc.)
    
    // Core data providers
    final dataProviders = [
      // Add actual provider references here
    ];
    
    for (final provider in dataProviders) {
      ref.invalidate(provider);
    }
  }

  static void _invalidateIncomeProviders(WidgetRef ref, String operation) {
    // For income operations, we need to invalidate:
    // 1. Income list (always)
    // 2. Summary data (always)
    // 3. Performance metrics (only if it affects calculations)
    
    switch (operation) {
      case 'create':
      case 'update':
      case 'delete':
        // These operations affect both list and summary
        // ref.invalidate(incomeListProvider);
        // ref.invalidate(incomeSummaryProvider);
        break;
    }
  }

  static void _invalidateOrderProviders(WidgetRef ref, String operation) {
    // For order operations, we need to invalidate:
    // 1. Order list (always)
    // 2. Performance metrics (always, as orders affect performance)
    // 3. Level calculations (if order affects level progress)
    
    switch (operation) {
      case 'create':
      case 'update':
      case 'delete':
        // ref.invalidate(orderListProvider);
        // ref.invalidate(performanceMetricsProvider);
        break;
    }
  }

  static void _invalidatePerformanceProviders(WidgetRef ref, String operation) {
    // Performance data changes affect:
    // 1. Performance list
    // 2. Level calculations
    // 3. Overall metrics
    
    switch (operation) {
      case 'create':
      case 'update':
      case 'delete':
        // ref.invalidate(performanceListProvider);
        // ref.invalidate(levelCalculationProvider);
        break;
    }
  }
}

/// Utility for tracking provider invalidation frequency (for debugging)
class InvalidationTracker {
  static final Map<String, int> _invalidationCounts = {};
  static final Map<String, DateTime> _lastInvalidationTimes = {};

  static void trackInvalidation(String providerName) {
    _invalidationCounts[providerName] = 
        (_invalidationCounts[providerName] ?? 0) + 1;
    _lastInvalidationTimes[providerName] = DateTime.now();
  }

  static Map<String, int> getInvalidationCounts() => 
      Map.unmodifiable(_invalidationCounts);

  static void resetTracking() {
    _invalidationCounts.clear();
    _lastInvalidationTimes.clear();
  }

  static void printInvalidationStats() {
    print('Provider Invalidation Statistics:');
    for (final entry in _invalidationCounts.entries) {
      print('${entry.key}: ${entry.value} invalidations');
    }
  }
}

/// Extension on WidgetRef for optimized invalidation methods
extension OptimizedInvalidation on WidgetRef {
  /// Invalidate with debouncing
  void invalidateDebounced(
    ProviderBase provider, {
    Duration threshold = const Duration(milliseconds: 500),
  }) {
    // Implementation would use OptimizedInvalidationMixin
    invalidate(provider);
  }

  /// Batch invalidate multiple providers
  void invalidateBatch(List<ProviderBase> providers) {
    for (final provider in providers) {
      invalidate(provider);
    }
  }

  /// Conditional invalidation
  void invalidateIf(ProviderBase provider, bool condition) {
    if (condition) {
      invalidate(provider);
    }
  }
}
