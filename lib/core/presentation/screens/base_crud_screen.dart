/// Base CRUD screen architecture for consistent screen implementation
///
/// This file provides abstract base classes for implementing CRUD screens
/// with consistent UI patterns, error handling, and loading states across
/// the application. It eliminates code duplication while maintaining
/// flexibility for entity-specific customizations.
///
/// Usage:
/// ```dart
/// class OrdersScreen extends BaseCrudScreen<Order, OrderSummary> {
///   const OrdersScreen({super.key});
///   @override
///   OrdersScreenState createState() => OrdersScreenState();
/// }
///
/// class OrdersScreenState extends BaseCrudScreenState<Order, OrderSummary, OrdersScreen> {
///   @override
///   String get screenTitle => 'Orders';
///
///   @override
///   Widget buildListItem(Order entity) => OrderListItem(order: entity);
///
///   // Implement other required abstract members...
/// }
/// ```
library;

import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../providers/global_date_range_provider.dart';
import '../../theme/app_colors.dart';
import '../../widgets/app_date_range_selector.dart';
import '../../widgets/app_error_container.dart';

/// Abstract base class for CRUD screens with consistent UI patterns
///
/// This class provides a foundation for implementing CRUD screens with
/// standardized structure and behavior. It uses generic type parameters
/// to maintain type safety while allowing entity-specific customizations.
///
/// Type Parameters:
/// - [TEntity]: The entity type (e.g., Order, Income, Performance)
/// - [TSummary]: The summary type (e.g., OrderSummary, IncomeSummary)
abstract class BaseCrudScreen<TEntity, TSummary>
    extends ConsumerStatefulWidget {
  const BaseCrudScreen({super.key});
}

/// Abstract state class for CRUD screens with core implementation
///
/// This class contains all the common logic for building CRUD screens,
/// including the app bar, summary sections, list sections, error handling,
/// and loading states. Concrete implementations only need to provide
/// entity-specific logic through abstract methods and properties.
///
/// Type Parameters:
/// - [TEntity]: The entity type
/// - [TSummary]: The summary type
/// - [TWidget]: The widget type for proper state binding
abstract class BaseCrudScreenState<
  TEntity,
  TSummary,
  TWidget extends BaseCrudScreen<TEntity, TSummary>
>
    extends ConsumerState<TWidget> {
  // Abstract properties that must be implemented by concrete classes

  /// The title displayed in the app bar
  String get screenTitle;

  /// Tooltip text for the add button in the app bar
  String get addButtonTooltip;

  /// Icon representing the entity type
  IconData get entityIcon;

  /// Human-readable name for the entity type
  String get entityName;

  // Abstract methods that must be implemented by concrete classes

  /// Provider for the list of entities
  AsyncValue<List<TEntity>> get entityListAsync;

  /// Provider for the summary data
  AsyncValue<TSummary> get summaryAsync;

  /// Build a list item widget for the given entity
  Widget buildListItem(TEntity entity);

  /// Navigate to the form screen for adding or editing an entity
  void navigateToForm({TEntity? entity});

  /// Delete the specified entity
  Future<void> deleteEntity(TEntity entity);

  /// Build the summary section for the given summary data
  Widget buildSummarySection(TSummary summary);

  /// Refresh data by invalidating providers
  void refreshData();

  /// Build the history header section (can be overridden for customization)
  Widget buildHistoryHeader() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.fromLTRB(16.0, 24.0, 16.0, 16.0),
        child: Text(
          'History',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
      ),
    );
  }

  /// Build loading state widget (can be overridden for customization)
  Widget buildLoadingState() {
    return const SliverFillRemaining(
      child: Center(child: CircularProgressIndicator()),
    );
  }

  /// Build error container widget
  Widget buildErrorContainer(Object error) {
    return AppErrorContainer(
      error: error,
      compact: true,
      onRetry: () {
        // Refresh the data when retry is pressed
        refreshData();
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final dateRangeAsync = ref.watch(globalDateRangeProvider);

    // Check if any of the async values are in loading state
    final bool isLoading =
        entityListAsync.isLoading ||
        summaryAsync.isLoading ||
        dateRangeAsync.isLoading;

    return Scaffold(
      body: RefreshIndicator(
        onRefresh: () async {
          // Refresh data using the concrete implementation
          refreshData();
        },
        child: CustomScrollView(
          slivers: [
            _buildAppBar(dateRangeAsync),

            // If loading, show loading state, otherwise show content
            if (isLoading)
              buildLoadingState()
            else ...[
              _buildSummarySection(),
              buildHistoryHeader(),
              _buildEntityListSection(),
            ],
          ],
        ),
      ),
    );
  }

  /// Build the app bar with date range selector
  Widget _buildAppBar(AsyncValue<DateTimeRange> dateRangeAsync) {
    return SliverAppBar(
      expandedHeight: 120.0,
      floating: false,
      pinned: true,
      backgroundColor: AppColors.primary,
      elevation: 0,
      title: Text(
        screenTitle,
        style: const TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.add, color: Colors.white),
          onPressed: () => navigateToForm(),
          tooltip: addButtonTooltip,
        ),
      ],
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [AppColors.primary, AppColors.primaryDark],
            ),
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.fromLTRB(16.0, 60.0, 16.0, 16.0),
              child: dateRangeAsync.maybeWhen(
                data: (dateRange) => AppDateRangeSelector.forAppBar(
                  dateRange: dateRange,
                  onDateRangeSelected: (newRange) => ref
                      .read(globalDateRangeProvider.notifier)
                      .setDateRange(newRange),
                ),
                orElse: () => const SizedBox.shrink(),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// Build the summary section
  Widget _buildSummarySection() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.fromLTRB(16.0, 16.0, 16.0, 0.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Summary',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            Padding(
              padding: const EdgeInsets.only(bottom: 12.0),
              child: Text(
                'Overview of your ${entityName.toLowerCase()} performance and data',
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
              ),
            ),
            summaryAsync.maybeWhen(
              data: (summary) => buildSummarySection(summary),
              error: (error, stack) => buildErrorContainer(error),
              orElse: () => const SizedBox.shrink(),
            ),
          ],
        ),
      ),
    );
  }

  /// Build the entity list section
  Widget _buildEntityListSection() {
    return entityListAsync.maybeWhen(
      data: (entityList) => _buildEntityList(entityList),
      error: (error, stack) =>
          SliverFillRemaining(child: Center(child: buildErrorContainer(error))),
      orElse: () => const SliverToBoxAdapter(child: SizedBox.shrink()),
    );
  }

  /// Build the entity list
  Widget _buildEntityList(List<TEntity> entityList) {
    if (entityList.isEmpty) {
      return SliverFillRemaining(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(entityIcon, size: 64, color: Colors.grey[400]),
              const SizedBox(height: 16),
              Text(
                'No ${entityName.toLowerCase()}s found',
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(color: Colors.grey[600]),
              ),
              const SizedBox(height: 8),
              Text(
                'Tap the + button to add your first ${entityName.toLowerCase()}',
                style: Theme.of(
                  context,
                ).textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
              ),
            ],
          ),
        ),
      );
    }

    return SliverList(
      delegate: SliverChildBuilderDelegate((context, index) {
        final entity = entityList[index];
        return buildListItem(entity);
      }, childCount: entityList.length),
    );
  }
}
